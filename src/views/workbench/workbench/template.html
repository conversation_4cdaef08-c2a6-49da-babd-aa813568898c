<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>数据管理系统 - 工作台</title>
  <!-- 引入外部资源 -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js"></script>
  
  <!-- Tailwind 配置 -->
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#165DFF',
            secondary: '#36BFFA',
            success: '#00B42A',
            warning: '#FF7D00',
            danger: '#F53F3F',
            info: '#86909C',
            dark: '#1D2129',
            light: '#F2F3F5'
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif'],
          },
        },
      }
    }
  </script>
  
  <!-- 自定义工具类 -->
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .card-shadow {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      }
      .sidebar-item-active {
        @apply bg-primary/10 text-primary border-l-4 border-primary;
      }
      .task-card-hover {
        @apply hover:shadow-lg hover:-translate-y-1 transition-all duration-300;
      }
    }
  </style>
</head>
<body class="font-inter bg-gray-50 text-dark overflow-hidden h-screen flex flex-col">
  <!-- 顶部导航栏 -->
  <header class="bg-white shadow-sm z-30 relative">
    <div class="flex items-center justify-between px-4 lg:px-6 h-16">
      <!-- 左侧Logo和标题 -->
      <div class="flex items-center">
        <button id="sidebar-toggle" class="p-2 mr-3 rounded-md lg:hidden text-gray-500 hover:text-primary hover:bg-gray-100 transition-all duration-300">
          <i class="fa fa-bars text-lg"></i>
        </button>
        <div class="flex items-center">
          <div class="w-8 h-8 rounded-md bg-primary flex items-center justify-center text-white">
            <i class="fa fa-database"></i>
          </div>
          <h1 class="ml-3 text-lg font-semibold">数据管理系统</h1>
        </div>
      </div>
      
      <!-- 右侧工具栏 -->
      <div class="flex items-center space-x-4">
        <!-- 搜索框 -->
        <div class="hidden md:flex items-center bg-gray-100 rounded-md px-3 py-1.5">
          <i class="fa fa-search text-gray-400 mr-2"></i>
          <input type="text" placeholder="搜索任务..." class="bg-transparent border-none outline-none text-sm w-40 lg:w-60">
        </div>
        
        <!-- 通知 -->
        <div class="relative">
          <button class="p-2 rounded-full hover:bg-gray-100 transition-all duration-300 relative">
            <i class="fa fa-bell-o text-gray-600"></i>
            <span class="absolute top-1 right-1 w-2 h-2 bg-danger rounded-full"></span>
          </button>
        </div>
        
        <!-- 用户信息 -->
        <div class="flex items-center">
          <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" class="w-8 h-8 rounded-full object-cover">
          <div class="ml-2 hidden md:block">
            <p class="text-sm font-medium">王管理员</p>
            <p class="text-xs text-gray-500">数据管理员</p>
          </div>
        </div>
      </div>
    </div>
  </header>

  <div class="flex flex-1 overflow-hidden">
    <!-- 侧边栏导航 -->
    <aside id="sidebar" class="w-64 bg-white shadow-sm transform -translate-x-full lg:translate-x-0 fixed lg:static h-[calc(100vh-4rem)] z-20 transition-all duration-300 ease-in-out">
      <nav class="py-4 h-full flex flex-col">
        <div class="px-4 mb-6">
          <p class="text-xs uppercase text-gray-400 font-medium">主导航</p>
        </div>
        
        <!-- 导航菜单 -->
        <ul class="flex-1 px-2 space-y-1 overflow-y-auto">
          <li>
            <a href="#" class="flex items-center px-4 py-3 text-sm rounded-md sidebar-item-active">
              <i class="fa fa-tachometer w-5 text-center"></i>
              <span class="ml-3">工作台</span>
            </a>
          </li>
          
          <li>
            <a href="#" class="flex items-center px-4 py-3 text-sm rounded-md hover:bg-gray-50 transition-all duration-300">
              <i class="fa fa-tasks w-5 text-center"></i>
              <span class="ml-3">任务管理</span>
            </a>
          </li>
          
          <li>
            <a href="#" class="flex items-center px-4 py-3 text-sm rounded-md hover:bg-gray-50 transition-all duration-300">
              <i class="fa fa-car w-5 text-center"></i>
              <span class="ml-3">车型+OE数据</span>
            </a>
          </li>
          
          <li>
            <a href="#" class="flex items-center px-4 py-3 text-sm rounded-md hover:bg-gray-50 transition-all duration-300">
              <i class="fa fa-cubes w-5 text-center"></i>
              <span class="ml-3">产品管理</span>
            </a>
          </li>
          
          <li>
            <a href="#" class="flex items-center px-4 py-3 text-sm rounded-md hover:bg-gray-50 transition-all duration-300">
              <i class="fa fa-file-text-o w-5 text-center"></i>
              <span class="ml-3">询报价管理</span>
            </a>
          </li>
          
          <li>
            <a href="#" class="flex items-center px-4 py-3 text-sm rounded-md hover:bg-gray-50 transition-all duration-300">
              <i class="fa fa-lightbulb-o w-5 text-center"></i>
              <span class="ml-3">新产品开发</span>
            </a>
          </li>
          
          <li>
            <a href="#" class="flex items-center px-4 py-3 text-sm rounded-md hover:bg-gray-50 transition-all duration-300">
              <i class="fa fa-users w-5 text-center"></i>
              <span class="ml-3">用户管理</span>
            </a>
          </li>
          
          <li>
            <a href="#" class="flex items-center px-4 py-3 text-sm rounded-md hover:bg-gray-50 transition-all duration-300">
              <i class="fa fa-bar-chart w-5 text-center"></i>
              <span class="ml-3">统计分析</span>
            </a>
          </li>
        </ul>
        
        <!-- 底部 -->
        <div class="mt-auto px-4 py-3 border-t">
          <div class="bg-gray-50 rounded-md p-3">
            <p class="text-xs text-gray-500 mb-2">系统版本</p>
            <p class="text-sm font-medium">v3.1.0</p>
          </div>
        </div>
      </nav>
    </aside>

    <!-- 主内容区域 -->
    <main class="flex-1 overflow-y-auto bg-gray-50 p-4 lg:p-6">
      <!-- 页面标题 -->
      <div class="mb-6">
        <h2 class="text-[clamp(1.25rem,3vw,1.75rem)] font-semibold">工作台</h2>
        <p class="text-gray-500 mt-1">欢迎回来，王管理员！创建和管理您的任务</p>
      </div>
      
      <!-- 快速创建任务 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <!-- 车型+OE数据任务 -->
        <div class="bg-white rounded-xl p-6 card-shadow task-card-hover border-l-4 border-primary cursor-pointer" id="create-oe-task">
          <div class="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center text-primary mb-4">
            <i class="fa fa-car text-xl"></i>
          </div>
          <h3 class="text-lg font-medium mb-2">车型+OE数据任务</h3>
          <p class="text-gray-500 text-sm mb-4">创建新的车型和OE码数据采集与整理任务</p>
          <div class="text-primary text-sm font-medium flex items-center">
            <span>创建任务</span>
            <i class="fa fa-arrow-right ml-2"></i>
          </div>
        </div>
        
        <!-- 产品新增/修改 -->
        <div class="bg-white rounded-xl p-6 card-shadow task-card-hover border-l-4 border-secondary cursor-pointer" id="create-product-task">
          <div class="w-12 h-12 rounded-lg bg-secondary/10 flex items-center justify-center text-secondary mb-4">
            <i class="fa fa-cubes text-xl"></i>
          </div>
          <h3 class="text-lg font-medium mb-2">产品管理任务</h3>
          <p class="text-gray-500 text-sm mb-4">创建新产品录入或现有产品信息修改任务</p>
          <div class="text-secondary text-sm font-medium flex items-center">
            <span>创建任务</span>
            <i class="fa fa-arrow-right ml-2"></i>
          </div>
        </div>
        
        <!-- 询报价任务 -->
        <div class="bg-white rounded-xl p-6 card-shadow task-card-hover border-l-4 border-warning cursor-pointer" id="create-quote-task">
          <div class="w-12 h-12 rounded-lg bg-warning/10 flex items-center justify-center text-warning mb-4">
            <i class="fa fa-file-text-o text-xl"></i>
          </div>
          <h3 class="text-lg font-medium mb-2">询报价任务</h3>
          <p class="text-gray-500 text-sm mb-4">创建新的询价单或处理报价相关任务</p>
          <div class="text-warning text-sm font-medium flex items-center">
            <span>创建任务</span>
            <i class="fa fa-arrow-right ml-2"></i>
          </div>
        </div>
        
        <!-- 新产品开发任务 -->
        <div class="bg-white rounded-xl p-6 card-shadow task-card-hover border-l-4 border-success cursor-pointer" id="create-development-task">
          <div class="w-12 h-12 rounded-lg bg-success/10 flex items-center justify-center text-success mb-4">
            <i class="fa fa-lightbulb-o text-xl"></i>
          </div>
          <h3 class="text-lg font-medium mb-2">新产品开发任务</h3>
          <p class="text-gray-500 text-sm mb-4">创建新产品研发与开发相关任务</p>
          <div class="text-success text-sm font-medium flex items-center">
            <span>创建任务</span>
            <i class="fa fa-arrow-right ml-2"></i>
          </div>
        </div>
      </div>
      
      <!-- 任务统计 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div class="bg-white rounded-lg p-5 card-shadow">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">总任务数</p>
              <h3 class="text-2xl font-semibold mt-1">328</h3>
            </div>
            <div class="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center text-primary">
              <i class="fa fa-tasks"></i>
            </div>
          </div>
          <p class="text-success text-sm mt-3 flex items-center">
            <i class="fa fa-arrow-up mr-1"></i> 12% <span class="text-gray-500 ml-1">较上月</span>
          </p>
        </div>
        
        <div class="bg-white rounded-lg p-5 card-shadow">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">进行中任务</p>
              <h3 class="text-2xl font-semibold mt-1">86</h3>
            </div>
            <div class="w-10 h-10 rounded-full bg-warning/10 flex items-center justify-center text-warning">
              <i class="fa fa-spinner"></i>
            </div>
          </div>
          <p class="text-danger text-sm mt-3 flex items-center">
            <i class="fa fa-arrow-up mr-1"></i> 5% <span class="text-gray-500 ml-1">较上周</span>
          </p>
        </div>
        
        <div class="bg-white rounded-lg p-5 card-shadow">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">已完成任务</p>
              <h3 class="text-2xl font-semibold mt-1">215</h3>
            </div>
            <div class="w-10 h-10 rounded-full bg-success/10 flex items-center justify-center text-success">
              <i class="fa fa-check"></i>
            </div>
          </div>
          <p class="text-success text-sm mt-3 flex items-center">
            <i class="fa fa-arrow-up mr-1"></i> 18% <span class="text-gray-500 ml-1">较上月</span>
          </p>
        </div>
        
        <div class="bg-white rounded-lg p-5 card-shadow">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">逾期任务</p>
              <h3 class="text-2xl font-semibold mt-1 text-danger">7</h3>
            </div>
            <div class="w-10 h-10 rounded-full bg-danger/10 flex items-center justify-center text-danger">
              <i class="fa fa-exclamation-triangle"></i>
            </div>
          </div>
          <p class="text-success text-sm mt-3 flex items-center">
            <i class="fa fa-arrow-down mr-1"></i> 3% <span class="text-gray-500 ml-1">较上周</span>
          </p>
        </div>
      </div>
      
      <!-- 图表和最近任务 -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <!-- 任务完成情况图表 -->
        <div class="lg:col-span-2 bg-white rounded-lg p-5 card-shadow">
          <div class="flex items-center justify-between mb-5">
            <h3 class="font-semibold">任务类型分布</h3>
            <div class="flex space-x-2">
              <button class="px-3 py-1 text-xs rounded-md bg-primary/10 text-primary">本月</button>
              <button class="px-3 py-1 text-xs rounded-md text-gray-500 hover:bg-gray-100">上月</button>
              <button class="px-3 py-1 text-xs rounded-md text-gray-500 hover:bg-gray-100">全年</button>
            </div>
          </div>
          <div class="h-80">
            <canvas id="taskTypeChart"></canvas>
          </div>
        </div>
        
        <!-- 任务进度 -->
        <div class="bg-white rounded-lg p-5 card-shadow">
          <h3 class="font-semibold mb-5">任务完成进度</h3>
          <div class="space-y-6">
            <div>
              <div class="flex justify-between mb-2">
                <span class="text-sm font-medium">车型+OE数据任务</span>
                <span class="text-sm text-gray-500">75%</span>
              </div>
              <div class="w-full bg-gray-100 rounded-full h-2.5">
                <div class="bg-primary h-2.5 rounded-full" style="width: 75%"></div>
              </div>
            </div>
            
            <div>
              <div class="flex justify-between mb-2">
                <span class="text-sm font-medium">产品管理任务</span>
                <span class="text-sm text-gray-500">62%</span>
              </div>
              <div class="w-full bg-gray-100 rounded-full h-2.5">
                <div class="bg-secondary h-2.5 rounded-full" style="width: 62%"></div>
              </div>
            </div>
            
            <div>
              <div class="flex justify-between mb-2">
                <span class="text-sm font-medium">询报价任务</span>
                <span class="text-sm text-gray-500">48%</span>
              </div>
              <div class="w-full bg-gray-100 rounded-full h-2.5">
                <div class="bg-warning h-2.5 rounded-full" style="width: 48%"></div>
              </div>
            </div>
            
            <div>
              <div class="flex justify-between mb-2">
                <span class="text-sm font-medium">新产品开发任务</span>
                <span class="text-sm text-gray-500">35%</span>
              </div>
              <div class="w-full bg-gray-100 rounded-full h-2.5">
                <div class="bg-success h-2.5 rounded-full" style="width: 35%"></div>
              </div>
            </div>
          </div>
          
          <div class="mt-6 pt-6 border-t">
            <h4 class="font-medium mb-4">任务完成率</h4>
            <div class="relative pt-1">
              <div class="flex mb-2 items-center justify-between">
                <div>
                  <span class="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-success bg-success/10">
                    总体完成率
                  </span>
                </div>
                <div class="text-right">
                  <span class="text-xs font-semibold inline-block text-success">
                    65.5%
                  </span>
                </div>
              </div>
              <div class="overflow-hidden h-2 mb-4 text-xs flex rounded bg-success/20">
                <div style="width: 65.5%" class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-success"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 最近任务列表 -->
      <div class="bg-white rounded-lg card-shadow overflow-hidden">
        <div class="flex items-center justify-between px-5 py-4 border-b">
          <h3 class="font-semibold">最近任务</h3>
          <a href="#" class="text-primary text-sm hover:underline">查看全部</a>
        </div>
        <div class="overflow-x-auto">
          <table class="min-w-full">
            <thead>
              <tr class="bg-gray-50">
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">任务名称</th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">任务类型</th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">负责人</th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">截止日期</th>
                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-100">
              <tr class="hover:bg-gray-50 transition-all duration-300">
                <td class="px-4 py-3 whitespace-nowrap">
                  <div class="text-sm font-medium">2023款车型数据录入</div>
                </td>
                <td class="px-4 py-3 whitespace-nowrap">
                  <span class="px-2 py-1 text-xs rounded-full bg-primary/10 text-primary">车型+OE</span>
                </td>
                <td class="px-4 py-3 whitespace-nowrap">
                  <div class="flex items-center">
                    <img src="https://picsum.photos/id/1012/32/32" alt="用户头像" class="w-6 h-6 rounded-full mr-2">
                    <span class="text-sm">李工程师</span>
                  </div>
                </td>
                <td class="px-4 py-3 whitespace-nowrap">
                  <span class="px-2 py-1 text-xs rounded-full bg-warning/10 text-warning">进行中</span>
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm">2023-07-15</td>
                <td class="px-4 py-3 whitespace-nowrap text-right text-sm">
                  <button class="text-primary hover:text-primary/80 mr-3">编辑</button>
                  <button class="text-gray-500 hover:text-gray-700">详情</button>
                </td>
              </tr>
              
              <tr class="hover:bg-gray-50 transition-all duration-300">
                <td class="px-4 py-3 whitespace-nowrap">
                  <div class="text-sm font-medium">制动系统产品信息更新</div>
                </td>
                <td class="px-4 py-3 whitespace-nowrap">
                  <span class="px-2 py-1 text-xs rounded-full bg-secondary/10 text-secondary">产品管理</span>
                </td>
                <td class="px-4 py-3 whitespace-nowrap">
                  <div class="flex items-center">
                    <img src="https://picsum.photos/id/1066/32/32" alt="用户头像" class="w-6 h-6 rounded-full mr-2">
                    <span class="text-sm">陈专员</span>
                  </div>
                </td>
                <td class="px-4 py-3 whitespace-nowrap">
                  <span class="px-2 py-1 text-xs rounded-full bg-success/10 text-success">已完成</span>
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm">2023-07-05</td>
                <td class="px-4 py-3 whitespace-nowrap text-right text-sm">
                  <button class="text-primary hover:text-primary/80 mr-3">编辑</button>
                  <button class="text-gray-500 hover:text-gray-700">详情</button>
                </td>
              </tr>
              
              <tr class="hover:bg-gray-50 transition-all duration-300">
                <td class="px-4 py-3 whitespace-nowrap">
                  <div class="text-sm font-medium">北京汽车配件询价</div>
                </td>
                <td class="px-4 py-3 whitespace-nowrap">
                  <span class="px-2 py-1 text-xs rounded-full bg-warning/10 text-warning">询报价</span>
                </td>
                <td class="px-4 py-3 whitespace-nowrap">
                  <div class="flex items-center">
                    <img src="https://picsum.photos/id/1074/32/32" alt="用户头像" class="w-6 h-6 rounded-full mr-2">
                    <span class="text-sm">刘销售</span>
                  </div>
                </td>
                <td class="px-4 py-3 whitespace-nowrap">
                  <span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-500">待处理</span>
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm">2023-07-20</td>
                <td class="px-4 py-3 whitespace-nowrap text-right text-sm">
                  <button class="text-primary hover:text-primary/80 mr-3">编辑</button>
                  <button class="text-gray-500 hover:text-gray-700">详情</button>
                </td>
              </tr>
              
              <tr class="hover:bg-gray-50 transition-all duration-300">
                <td class="px-4 py-3 whitespace-nowrap">
                  <div class="text-sm font-medium">新能源汽车电池配件开发</div>
                </td>
                <td class="px-4 py-3 whitespace-nowrap">
                  <span class="px-2 py-1 text-xs rounded-full bg-success/10 text-success">新产品开发</span>
                </td>
                <td class="px-4 py-3 whitespace-nowrap">
                  <div class="flex items-center">
                    <img src="https://picsum.photos/id/1083/32/32" alt="用户头像" class="w-6 h-6 rounded-full mr-2">
                    <span class="text-sm">郑经理</span>
                  </div>
                </td>
                <td class="px-4 py-3 whitespace-nowrap">
                  <span class="px-2 py-1 text-xs rounded-full bg-warning/10 text-warning">进行中</span>
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm">2023-09-30</td>
                <td class="px-4 py-3 whitespace-nowrap text-right text-sm">
                  <button class="text-primary hover:text-primary/80 mr-3">编辑</button>
                  <button class="text-gray-500 hover:text-gray-700">详情</button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <!-- 分页 -->
        <div class="flex items-center justify-between px-5 py-4 border-t">
          <div class="text-sm text-gray-500">
            显示 1 到 4 条，共 128 条
          </div>
          <div class="flex space-x-1">
            <button class="w-8 h-8 flex items-center justify-center rounded border text-gray-500 hover:border-primary hover:text-primary disabled:opacity-50 disabled:cursor-not-allowed" disabled>
              <i class="fa fa-chevron-left text-xs"></i>
            </button>
            <button class="w-8 h-8 flex items-center justify-center rounded bg-primary text-white">1</button>
            <button class="w-8 h-8 flex items-center justify-center rounded border text-gray-700 hover:border-primary hover:text-primary">2</button>
            <button class="w-8 h-8 flex items-center justify-center rounded border text-gray-700 hover:border-primary hover:text-primary">3</button>
            <button class="w-8 h-8 flex items-center justify-center rounded border text-gray-700 hover:border-primary hover:text-primary">...</button>
            <button class="w-8 h-8 flex items-center justify-center rounded border text-gray-700 hover:border-primary hover:text-primary">32</button>
            <button class="w-8 h-8 flex items-center justify-center rounded border text-gray-500 hover:border-primary hover:text-primary">
              <i class="fa fa-chevron-right text-xs"></i>
            </button>
          </div>
        </div>
      </div>
    </main>
  </div>

  <!-- 任务创建模态框 (默认隐藏) -->
  <div id="task-modal" class="fixed inset-0 z-50 flex items-center justify-center hidden">
    <div class="absolute inset-0 bg-black bg-opacity-50 transition-opacity" id="modal-backdrop"></div>
    <div class="bg-white rounded-lg w-full max-w-2xl mx-4 shadow-xl transform transition-all z-10" id="modal-content">
      <div class="flex justify-between items-center px-6 py-4 border-b">
        <h3 class="text-lg font-semibold" id="modal-title">创建新任务</h3>
        <button id="close-modal" class="text-gray-500 hover:text-gray-700">
          <i class="fa fa-times"></i>
        </button>
      </div>
      
      <div class="p-6" id="modal-body">
        <!-- 任务表单内容将通过JavaScript动态生成 -->
      </div>
      
      <div class="flex justify-end px-6 py-4 border-t space-x-3">
        <button id="cancel-task" class="px-4 py-2 border rounded-md text-sm hover:bg-gray-50 transition-colors">
          取消
        </button>
        <button id="save-task" class="px-4 py-2 bg-primary text-white rounded-md text-sm hover:bg-primary/90 transition-colors">
          保存任务
        </button>
      </div>
    </div>
  </div>

  <!-- 遮罩层 -->
  <div id="sidebar-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-10 hidden lg:hidden"></div>

  <script>
    // 侧边栏切换
    const sidebarToggle = document.getElementById('sidebar-toggle');
    const sidebar = document.getElementById('sidebar');
    const sidebarOverlay = document.getElementById('sidebar-overlay');
    
    sidebarToggle.addEventListener('click', () => {
      sidebar.classList.toggle('-translate-x-full');
      sidebarOverlay.classList.toggle('hidden');
    });
    
    sidebarOverlay.addEventListener('click', () => {
      sidebar.classList.add('-translate-x-full');
      sidebarOverlay.classList.add('hidden');
    });
    
    // 任务类型图表
    const taskTypeCtx = document.getElementById('taskTypeChart').getContext('2d');
    const taskTypeChart = new Chart(taskTypeCtx, {
      type: 'bar',
      data: {
        labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
        datasets: [
          {
            label: '车型+OE数据任务',
            data: [18, 25, 22, 30, 28, 35],
            backgroundColor: '#165DFF',
            borderRadius: 4
          },
          {
            label: '产品管理任务',
            data: [15, 20, 18, 22, 25, 28],
            backgroundColor: '#36BFFA',
            borderRadius: 4
          },
          {
            label: '询报价任务',
            data: [12, 15, 18, 16, 20, 22],
            backgroundColor: '#FF7D00',
            borderRadius: 4
          },
          {
            label: '新产品开发任务',
            data: [8, 10, 12, 15, 10, 14],
            backgroundColor: '#00B42A',
            borderRadius: 4
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'top',
            labels: {
              boxWidth: 10,
              usePointStyle: true,
              pointStyle: 'circle'
            }
          }
        },
        scales: {
          x: {
            stacked: false,
            grid: {
              display: false
            }
          },
          y: {
            stacked: false,
            beginAtZero: true,
            grid: {
              color: 'rgba(0, 0, 0, 0.05)'
            }
          }
        }
      }
    });
    
    // 任务创建模态框逻辑
    const taskModal = document.getElementById('task-modal');
    const modalBackdrop = document.getElementById('modal-backdrop');
    const modalContent = document.getElementById('modal-content');
    const modalTitle = document.getElementById('modal-title');
    const modalBody = document.getElementById('modal-body');
    const closeModal = document.getElementById('close-modal');
    const cancelTask = document.getElementById('cancel-task');
    const saveTask = document.getElementById('save-task');
    
    // 打开模态框
    function openModal(taskType) {
      // 设置模态框标题
      modalTitle.textContent = `创建${taskType}任务`;
      
      // 根据任务类型生成不同的表单内容
      let formContent = '';
      
      switch(taskType) {
        case '车型+OE数据':
          formContent = `
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">任务名称</label>
                <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50" placeholder="例如：2023款车型数据录入">
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">车型品牌</label>
                <select class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                  <option value="">请选择品牌</option>
                  <option value="1">奔驰</option>
                  <option value="2">宝马</option>
                  <option value="3">奥迪</option>
                  <option value="4">大众</option>
                  <option value="5">丰田</option>
                  <option value="6">其他</option>
                </select>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">任务描述</label>
                <textarea rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50" placeholder="请描述任务详情..."></textarea>
              </div>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">负责人</label>
                  <select class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                    <option value="">请选择负责人</option>
                    <option value="1">李工程师</option>
                    <option value="2">王技术员</option>
                    <option value="3">赵专员</option>
                  </select>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">截止日期</label>
                  <input type="date" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">优先级</label>
                <div class="flex space-x-4">
                  <label class="inline-flex items-center">
                    <input type="radio" name="priority" value="low" class="text-primary focus:ring-primary">
                    <span class="ml-2 text-sm text-gray-700">低</span>
                  </label>
                  <label class="inline-flex items-center">
                    <input type="radio" name="priority" value="medium" class="text-primary focus:ring-primary" checked>
                    <span class="ml-2 text-sm text-gray-700">中</span>
                  </label>
                  <label class="inline-flex items-center">
                    <input type="radio" name="priority" value="high" class="text-primary focus:ring-primary">
                    <span class="ml-2 text-sm text-gray-700">高</span>
                  </label>
                </div>
              </div>
            </div>
          `;
          break;
          
        case '产品管理':
          formContent = `
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">任务类型</label>
                <div class="flex space-x-4">
                  <label class="inline-flex items-center">
                    <input type="radio" name="productTaskType" value="new" class="text-secondary focus:ring-secondary" checked>
                    <span class="ml-2 text-sm text-gray-700">新产品录入</span>
                  </label>
                  <label class="inline-flex items-center">
                    <input type="radio" name="productTaskType" value="edit" class="text-secondary focus:ring-secondary">
                    <span class="ml-2 text-sm text-gray-700">产品信息修改</span>
                  </label>
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">产品名称</label>
                <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-secondary/50" placeholder="输入产品名称">
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">产品类别</label>
                <select class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-secondary/50">
                  <option value="">请选择产品类别</option>
                  <option value="1">发动机配件</option>
                  <option value="2">制动系统</option>
                  <option value="3">悬挂系统</option>
                  <option value="4">电气系统</option>
                  <option value="5">车身配件</option>
                </select>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">任务描述</label>
                <textarea rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-secondary/50" placeholder="请描述需要录入或修改的内容..."></textarea>
              </div>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">负责人</label>
                  <select class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-secondary/50">
                    <option value="">请选择负责人</option>
                    <option value="1">陈专员</option>
                    <option value="2">刘工程师</option>
                    <option value="3">孙工</option>
                  </select>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">截止日期</label>
                  <input type="date" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-secondary/50">
                </div>
              </div>
            </div>
          `;
          break;
          
        case '询报价':
          formContent = `
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">询价单号</label>
                <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-warning/50" placeholder="自动生成，可手动修改">
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">客户名称</label>
                <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-warning/50" placeholder="输入客户名称">
              </div>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">联系人</label>
                  <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-warning/50" placeholder="客户联系人">
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">联系电话</label>
                  <input type="tel" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-warning/50" placeholder="联系电话">
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">询价产品</label>
                <div class="border border-gray-300 rounded-md p-3 bg-gray-50">
                  <div class="flex items-center justify-between mb-3">
                    <div class="flex-1 mr-4">
                      <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-warning/50" placeholder="产品名称">
                    </div>
                    <div class="w-32">
                      <input type="number" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-warning/50" placeholder="数量">
                    </div>
                  </div>
                  <button class="text-warning text-sm flex items-center">
                    <i class="fa fa-plus-circle mr-1"></i> 添加更多产品
                  </button>
                </div>
              </div>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">负责人</label>
                  <select class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-warning/50">
                    <option value="">请选择负责人</option>
                    <option value="1">刘销售</option>
                    <option value="2">张销售</option>
                    <option value="3">周销售</option>
                  </select>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">期望报价日期</label>
                  <input type="date" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-warning/50">
                </div>
              </div>
            </div>
          `;
          break;
          
        case '新产品开发':
          formContent = `
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">项目名称</label>
                <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-success/50" placeholder="例如：新能源汽车电池配件开发">
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">产品类别</label>
                <select class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-success/50">
                  <option value="">请选择产品类别</option>
                  <option value="1">发动机配件</option>
                  <option value="2">制动系统</option>
                  <option value="3">悬挂系统</option>
                  <option value="4">电气系统</option>
                  <option value="5">车身配件</option>
                </select>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">开发目标与描述</label>
                <textarea rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-success/50" placeholder="请描述开发目标和具体要求..."></textarea>
              </div>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">项目经理</label>
                  <select class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-success/50">
                    <option value="">请选择项目经理</option>
                    <option value="1">郑经理</option>
                    <option value="2">吴工</option>
                    <option value="3">钱工</option>
                  </select>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">预计完成日期</label>
                  <input type="date" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-success/50">
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">预算范围</label>
                <input type="text" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-success/50" placeholder="输入项目预算范围">
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">附件上传</label>
                <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                  <div class="space-y-1 text-center">
                    <i class="fa fa-cloud-upload text-gray-400 text-3xl"></i>
                    <div class="flex text-sm text-gray-600">
                      <label for="file-upload" class="relative cursor-pointer bg-white rounded-md font-medium text-success hover:text-success/80">
                        <span>上传文件</span>
                        <input id="file-upload" name="file-upload" type="file" class="sr-only">
                      </label>
                      <p class="pl-1">或拖放文件</p>
                    </div>
                    <p class="text-xs text-gray-500">支持 PDF, Word, Excel 格式</p>
                  </div>
                </div>
              </div>
            </div>
          `;
          break;
      }
      
      // 设置模态框内容
      modalBody.innerHTML = formContent;
      
      // 显示模态框
      taskModal.classList.remove('hidden');
      
      // 添加动画效果
      setTimeout(() => {
        modalBackdrop.classList.add('opacity-100');
        modalContent.classList.add('scale-100');
        modalContent.classList.remove('scale-95');
      }, 10);
    }
    
    // 关闭模态框
    function closeModalFunc() {
      modalBackdrop.classList.remove('opacity-100');
      modalContent.classList.remove('scale-100');
      modalContent.classList.add('scale-95');
      
      setTimeout(() => {
        taskModal.classList.add('hidden');
      }, 300);
    }
    
    // 绑定事件
    closeModal.addEventListener('click', closeModalFunc);
    cancelTask.addEventListener('click', closeModalFunc);
    modalBackdrop.addEventListener('click', closeModalFunc);
    
    // 保存任务
    saveTask.addEventListener('click', () => {
      // 这里添加保存任务的逻辑
      alert('任务创建成功！');
      closeModalFunc();
    });
    
    // 任务卡片点击事件
    document.getElementById('create-oe-task').addEventListener('click', () => {
      openModal('车型+OE数据');
    });
    
    document.getElementById('create-product-task').addEventListener('click', () => {
      openModal('产品管理');
    });
    
    document.getElementById('create-quote-task').addEventListener('click', () => {
      openModal('询报价');
    });
    
    document.getElementById('create-development-task').addEventListener('click', () => {
      openModal('新产品开发');
    });
  </script>
</body>
</html>
    